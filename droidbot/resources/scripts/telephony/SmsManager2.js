/**
 * Created by <PERSON><PERSON><PERSON> on 2020/4/20.
 */
Java.perform(function() {
    var cn = "android.telephony.gsm.SmsManager";
    var target = Java.use(cn);
    if (target) {
        target.divideMessage.implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "SENSITIVE"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "divideMessage";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.divideMessage.apply(this, arguments);
        };

        target.sendDataMessage.implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "SENSITIVE"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "sendDataMessage";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.sendDataMessage.apply(this, arguments);
        };

        target.sendMultipartTextMessage.implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "SENSITIVE"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "sendMultipartTextMessage";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.sendMultipartTextMessage.apply(this, arguments);
        };

        target.sendMultipartTextMessage.implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "SENSITIVE"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "sendMultipartTextMessage";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.sendMultipartTextMessage.apply(this, arguments);
        };
    }
});