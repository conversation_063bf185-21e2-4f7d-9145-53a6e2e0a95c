/**
 * Created by ma<PERSON><PERSON> on 2020/4/23.
 */
Java.perform(function() {
    var cn = "java.net.URLConnection";
    var target = Java.use(cn);
    if (target) {
        target.getHeaderField.overloads[0].implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "INTERESTED"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "getHeaderField";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.getHeaderField.overloads[0].apply(this, arguments);
        };
        target.getHeaderField.overloads[1].implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "INTERESTED"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "getHeaderField";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.getHeaderField.overloads[1].apply(this, arguments);
        };

        target.setDoInput.implementation = function(dest) {
            var myArray=new Array()
            myArray[0] = "INTERESTED"  //INTERESTED & SENSITIVE
            myArray[1] = cn + "." + "setDoInput";
            myArray[2] = Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()).split('\n\tat');
            send(myArray);
            return this.setDoInput.apply(this, arguments);
        };
    }
});